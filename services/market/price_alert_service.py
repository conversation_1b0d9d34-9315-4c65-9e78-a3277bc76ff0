"""
Price Alert Service - Monitors cryptocurrency prices and sends alerts when thresholds are met
"""

import logging
import time
import threading
from datetime import datetime, timezone
from typing import Dict, List, Any, Callable, Optional, Union

from utils.config import load_config, save_config

logger = logging.getLogger(__name__)

class PriceAlertService:
    """Service for monitoring cryptocurrency prices and triggering alerts"""
    
    def __init__(self):
        """Initialize the price alert service"""
        self.price_targets = {}  # {symbol: [target_prices]} - configured target prices
        self.daily_change_thresholds = []  # Percentage thresholds for daily change alerts
        self.alert_lock = threading.RLock()
        
        # Daily cooldown mechanism - Reset at 0h UTC
        self.daily_price_alerts = {}  # {symbol_price: date_string} - tracks price target alerts
        self.daily_change_alerts = {}  # {symbol_threshold: date_string} - tracks daily change alerts
        
        # Keep previous prices to detect crossings
        self.previous_prices = {}  # {symbol: price}
        
        # Keep track of last market data
        self.last_market_data = {}  # {symbol: {'daily_change': float, ...}}
        
        # Alert callbacks
        self.alert_callbacks = []
        
        # Load initial configuration
        self._load_config()
    
    def _load_config(self):
        """Load price alert configuration from config.yaml"""
        config = load_config()
        price_alerts_config = config.get('price_alerts', {})
        
        # Load daily change thresholds
        self.daily_change_thresholds = price_alerts_config.get('daily_change_thresholds', [5, 10, 15])
        
        # Load saved price targets if available
        self.price_targets = price_alerts_config.get('price_targets', {})
        
        logger.info(f"Loaded price alert configuration with {len(self.daily_change_thresholds)} daily change thresholds")
        logger.info(f"Loaded {sum(len(targets) for targets in self.price_targets.values())} price targets for {len(self.price_targets)} symbols")
    
    def register_alert_callback(self, callback: Callable):
        """Register a callback function for price alerts
        
        Args:
            callback: Async function with signature: async def callback(alert_type: str, alert_data: Dict)
        """
        self.alert_callbacks.append(callback)
        logger.info(f"Registered price alert callback: {callback.__name__}")
    
    async def update_market_data(self, market_data: Dict[str, Dict[str, Any]]):
        """Update stored market data for all symbols
        
        Args:
            market_data: Dictionary mapping symbols to their market data 
                        {symbol: {'daily_change': float, ...}}
        """
        self.last_market_data = market_data
        logger.debug(f"Updated market data for {len(market_data)} symbols")
    
    async def check_price_alerts(self, current_prices: Dict[str, float], 
                               market_data: Optional[Dict[str, Dict[str, Any]]] = None):
        """Check for price alert conditions and trigger alerts if needed
        
        Args:
            current_prices: Dictionary mapping symbols to their current prices
            market_data: Optional dictionary with additional market data (daily change, etc.)
                        If not provided, will use last stored market_data
        """
        if market_data:
            await self.update_market_data(market_data)
        else:
            market_data = self.last_market_data
        
        # Get current UTC date for cooldown checking
        current_utc_date = datetime.now(timezone.utc).strftime('%Y-%m-%d')
        
        # Process each symbol with current price
        for symbol, current_price in current_prices.items():
            # Skip if no previous price (first run)
            if symbol not in self.previous_prices:
                self.previous_prices[symbol] = current_price
                continue
            
            previous_price = self.previous_prices[symbol]
            
            # Check price target alerts
            await self._check_price_target_alerts(symbol, previous_price, current_price, current_utc_date)
            
            # Check daily change alerts
            if symbol in market_data:
                daily_change = market_data[symbol].get('daily_change', 0.0)
                await self._check_daily_change_alerts(symbol, daily_change, current_price, current_utc_date)
            
            # Update previous price
            self.previous_prices[symbol] = current_price
        
        # Clean up old alerts
        self._cleanup_old_alerts()
    
    async def _check_price_target_alerts(self, symbol: str, previous_price: float, 
                                       current_price: float, current_utc_date: str):
        """Check if any configured price targets are crossed and trigger alerts
        
        Args:
            symbol: The cryptocurrency symbol
            previous_price: Previous recorded price
            current_price: Current price
            current_utc_date: Current UTC date string for cooldown tracking
        """
        # Skip if no price targets configured for this symbol
        if symbol not in self.price_targets or not self.price_targets[symbol]:
            return
        
        # Check each target price
        for target_price in self.price_targets[symbol]:
            # Check if price crossed the target (from either direction)
            if ((previous_price < target_price and current_price >= target_price) or
                (previous_price > target_price and current_price <= target_price)):
                
                # Create alert key for cooldown tracking
                alert_key = f"{symbol}_{target_price}"
                
                # Check if already alerted today
                if alert_key in self.daily_price_alerts:
                    if self.daily_price_alerts[alert_key] == current_utc_date:
                        logger.debug(f"Skipping price target alert for {symbol} at {target_price} - already sent today")
                        continue
                
                # Record alert for today
                self.daily_price_alerts[alert_key] = current_utc_date
                
                # Determine direction
                direction = "above" if current_price >= target_price else "below"
                
                # Create alert data
                alert_data = {
                    "symbol": symbol,
                    "target_price": target_price,
                    "current_price": current_price,
                    "previous_price": previous_price,
                    "direction": direction,
                    "alert_reason": "price_target",
                    "percentage_change": ((current_price - previous_price) / previous_price) * 100
                }
                
                # Trigger alert
                await self._trigger_alert("price_target", alert_data)
                
                logger.info(f"Price target alert triggered for {symbol} at {target_price} ({direction})")
    
    async def _check_daily_change_alerts(self, symbol: str, daily_change: float, 
                                       current_price: float, current_utc_date: str):
        """Check if daily change percentage exceeds thresholds and trigger alerts
        
        Args:
            symbol: The cryptocurrency symbol
            daily_change: The 24h percentage change
            current_price: Current price
            current_utc_date: Current UTC date string for cooldown tracking
        """
        # Use absolute value for thresholds
        abs_daily_change = abs(daily_change)
        
        # Check each configured threshold
        for threshold in self.daily_change_thresholds:
            # Skip if change is less than threshold
            if abs_daily_change < threshold:
                continue
            
            # Create alert key for cooldown tracking
            alert_key = f"{symbol}_{threshold}"
            
            # Check if already alerted today
            if alert_key in self.daily_change_alerts:
                if self.daily_change_alerts[alert_key] == current_utc_date:
                    logger.debug(f"Skipping daily change alert for {symbol} at {threshold}% - already sent today")
                    continue
            
            # Record alert for today
            self.daily_change_alerts[alert_key] = current_utc_date
            
            # Create alert data
            alert_data = {
                "symbol": symbol,
                "threshold": threshold,
                "daily_change": daily_change,
                "current_price": current_price,
                "alert_reason": "daily_change"
            }
            
            # Trigger alert
            await self._trigger_alert("daily_change", alert_data)
            
            logger.info(f"Daily change alert triggered for {symbol} with {daily_change:.2f}% change (threshold: {threshold}%)")
    
    def _cleanup_old_alerts(self):
        """Clean up old alert records to prevent memory leaks"""
        current_utc_date = datetime.now(timezone.utc).strftime('%Y-%m-%d')
        
        # Clean up daily price target alerts from previous days
        self.daily_price_alerts = {
            alert_key: date_str for alert_key, date_str in self.daily_price_alerts.items()
            if date_str == current_utc_date
        }
        
        # Clean up daily change alerts from previous days
        self.daily_change_alerts = {
            alert_key: date_str for alert_key, date_str in self.daily_change_alerts.items()
            if date_str == current_utc_date
        }
    
    async def add_price_target(self, symbol: str, target_price: float) -> bool:
        """Add a price target for a symbol
        
        Args:
            symbol: The cryptocurrency symbol
            target_price: The target price to alert on
            
        Returns:
            bool: True if added successfully, False if already exists
        """
        with self.alert_lock:
            # Initialize list for symbol if not exists
            if symbol not in self.price_targets:
                self.price_targets[symbol] = []
            
            # Check if target already exists
            if target_price in self.price_targets[symbol]:
                return False
            
            # Add target
            self.price_targets[symbol].append(target_price)
            
            # Sort targets for consistency
            self.price_targets[symbol].sort()
            
            logger.info(f"Added price target for {symbol}: {target_price}")
            return True
    
    async def remove_price_target(self, symbol: str, target_price: float) -> bool:
        """Remove a price target for a symbol
        
        Args:
            symbol: The cryptocurrency symbol
            target_price: The target price to remove
            
        Returns:
            bool: True if removed successfully, False if not found
        """
        with self.alert_lock:
            # Check if symbol exists
            if symbol not in self.price_targets:
                return False
            
            # Check if target exists
            if target_price not in self.price_targets[symbol]:
                return False
            
            # Remove target
            self.price_targets[symbol].remove(target_price)
            
            # Remove symbol if no targets left
            if not self.price_targets[symbol]:
                del self.price_targets[symbol]
            
            logger.info(f"Removed price target for {symbol}: {target_price}")
            return True
    
    async def get_price_targets(self, symbol: Optional[str] = None) -> Dict[str, List[float]]:
        """Get configured price targets
        
        Args:
            symbol: Optional symbol to filter targets
            
        Returns:
            Dict mapping symbols to their target prices
        """
        with self.alert_lock:
            if symbol:
                return {symbol: self.price_targets.get(symbol, [])}
            else:
                return dict(self.price_targets)
    
    async def save_price_targets(self):
        """Save price targets to config file"""
        try:
            config = load_config()
            
            # Update price targets in config
            if 'price_alerts' not in config:
                config['price_alerts'] = {}
            
            config['price_alerts']['price_targets'] = self.price_targets
            
            # Save to config.yaml
            success = save_config(config)
            
            if success:
                logger.info(f"Saved {sum(len(targets) for targets in self.price_targets.values())} "
                          f"price targets for {len(self.price_targets)} symbols")
            else:
                logger.error("Failed to save price targets to config")
            
            return success
        except Exception as e:
            logger.error(f"Error saving price targets: {e}")
            return False
    
    async def _trigger_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Trigger alert callbacks
        
        Args:
            alert_type: Type of alert (price_target, daily_change)
            alert_data: Alert data dictionary
        """
        for callback in self.alert_callbacks:
            try:
                await callback(alert_type, alert_data)
            except Exception as e:
                logger.error(f"Error in price alert callback: {e}")


# Singleton instance
_price_alert_service = None

def get_price_alert_service() -> PriceAlertService:
    """Get or create the price alert service singleton
    
    Returns:
        PriceAlertService instance
    """
    global _price_alert_service
    if _price_alert_service is None:
        _price_alert_service = PriceAlertService()
    return _price_alert_service 