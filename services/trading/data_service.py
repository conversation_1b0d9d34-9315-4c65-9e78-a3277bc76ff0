#!/usr/bin/env python3
"""
Simple Data Service for Trading Bot
Fetches account, positions, and orders data every 60 seconds
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List

logger = logging.getLogger(__name__)

class TradingDataService:
    def __init__(self, trading_service=None):
        self.trading_service = trading_service
        self.last_update = None
        self.cached_data = {
            'account': {},
            'positions': [],
            'orders': []
        }
        self.update_interval = 60  # seconds
        self.running = False
        self.update_task = None

    def set_trading_service(self, trading_service):
        """Set trading service instance"""
        self.trading_service = trading_service

    async def start_data_updates(self):
        """Start periodic data updates"""
        if self.running:
            return
        
        self.running = True
        self.update_task = asyncio.create_task(self._update_loop())
        logger.info("🔄 Trading data service started - updating every 60 seconds")

    async def stop_data_updates(self):
        """Stop periodic data updates"""
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        logger.info("⏹️ Trading data service stopped")

    async def _update_loop(self):
        """Main update loop"""
        while self.running:
            try:
                await self.update_all_data()
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in data update loop: {e}")
                await asyncio.sleep(5)  # Short delay before retry

    async def update_all_data(self):
        """Update all trading data"""
        if not self.trading_service:
            logger.warning("⚠️ Trading service not available")
            return

        try:
            # Update account info
            account_result = self.trading_service.get_account_balance()
            if account_result.get('success'):
                self.cached_data['account'] = account_result
            
            # Update positions
            positions_result = self.trading_service.get_positions()
            if positions_result.get('success'):
                # Filter only positions with non-zero amounts
                active_positions = []
                for pos in positions_result.get('positions', []):
                    if float(pos.get('contracts', 0)) != 0:
                        # Determine position side based on contracts sign
                        contracts = float(pos.get('contracts', 0))
                        position_side = 'LONG' if contracts > 0 else 'SHORT'

                        active_positions.append({
                            'symbol': pos.get('symbol'),
                            'side': pos.get('side'),
                            'position_side': position_side,
                            'size': abs(contracts),
                            'entry_price': float(pos.get('entryPrice', 0)),
                            'mark_price': float(pos.get('markPrice', 0)),
                            'unrealized_pnl': float(pos.get('unrealizedPnl', 0)),
                            'percentage': float(pos.get('percentage', 0))
                        })
                self.cached_data['positions'] = active_positions
            
            # Update open orders
            orders_result = self.trading_service.get_open_orders()
            if orders_result.get('success'):
                formatted_orders = []
                for order in orders_result.get('orders', []):
                    formatted_orders.append({
                        'id': order.get('id'),
                        'symbol': order.get('symbol'),
                        'side': order.get('side'),
                        'type': order.get('type'),
                        'amount': float(order.get('amount', 0)),
                        'price': float(order.get('price', 0)),
                        'status': order.get('status')
                    })
                self.cached_data['orders'] = formatted_orders

            self.last_update = datetime.now(timezone.utc)
            logger.debug(f"📊 Data updated: {len(self.cached_data['positions'])} positions, {len(self.cached_data['orders'])} orders")

        except Exception as e:
            logger.error(f"❌ Error updating trading data: {e}")

    def get_account_data(self) -> Dict:
        """Get cached account data"""
        return self.cached_data.get('account', {})

    def get_positions_data(self) -> List[Dict]:
        """Get cached positions data"""
        return self.cached_data.get('positions', [])

    def get_orders_data(self) -> List[Dict]:
        """Get cached orders data"""
        return self.cached_data.get('orders', [])

    def get_summary_data(self) -> Dict:
        """Get summary of all data for status dashboard"""
        account = self.get_account_data()
        positions = self.get_positions_data()
        orders = self.get_orders_data()

        # Calculate totals
        total_unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in positions)
        
        return {
            'account': {
                'total_balance': account.get('total_usdt', 0),
                'free_balance': account.get('free_usdt', 0),
                'margin_used': account.get('used_usdt', 0)
            },
            'positions': {
                'count': len(positions),
                'total_unrealized_pnl': total_unrealized_pnl,
                'list': positions
            },
            'orders': {
                'count': len(orders),
                'list': orders
            },
            'last_update': self.last_update.isoformat() if self.last_update else None
        }

    def is_data_fresh(self, max_age_seconds: int = 120) -> bool:
        """Check if cached data is fresh"""
        if not self.last_update:
            return False
        
        age = (datetime.now(timezone.utc) - self.last_update).total_seconds()
        return age <= max_age_seconds

    async def force_update(self):
        """Force immediate data update"""
        await self.update_all_data()

# Global instance
trading_data_service = TradingDataService()
